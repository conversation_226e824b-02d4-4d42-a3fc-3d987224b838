import 'package:flutter/material.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/accordion_controller.dart';
import 'package:nsl/screens/web/new_design/widgets/hover_nav_item.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';

class GlobalLibraryAccordionFlow extends StatefulWidget {
  const GlobalLibraryAccordionFlow({super.key});

  @override
  State<GlobalLibraryAccordionFlow> createState() => _GlobalLibraryAccordionFlowState();
}

class _GlobalLibraryAccordionFlowState extends State<GlobalLibraryAccordionFlow> {
  late AccordionController _accordionController;
  String? _selectedIndustry;
  final TextEditingController _searchController = TextEditingController();
  List<AccordionItem> _filteredAccordionItems = [];

  final List<String> _industries = [
    'Select Industry',
    'Technology',
    'Healthcare',
    'Finance',
    'Education',
    'Manufacturing',
    'Retail',
    'Real Estate'
  ];

  final List<AccordionItem> _accordionItems = [
    AccordionItem(
      id: 'employee',
      title: 'Employee',
      subtitle: 'Employee is in Sale, Finance and Development Department, inherits none, reports to Manager',
      children: ['Employee Details', 'Department Info', 'Reporting Structure'],
    ),
    AccordionItem(
      id: 'manager',
      title: 'Manager',
      subtitle: null,
      children: ['Manager Responsibilities', 'Team Management', 'Performance Reviews'],
    ),
    AccordionItem(
      id: 'senior_manager',
      title: 'Senior Manager',
      subtitle: null,
      children: ['Strategic Planning', 'Budget Management', 'Leadership Development'],
    ),
    AccordionItem(
      id: 'go_name_1',
      title: 'GO Name',
      subtitle: null,
      children: ['GO Details 1', 'Configuration 1', 'Settings 1'],
    ),
    AccordionItem(
      id: 'go_name_2',
      title: 'GO Name',
      subtitle: null,
      children: ['GO Details 2', 'Configuration 2', 'Settings 2'],
    ),
    AccordionItem(
      id: 'go_name_3',
      title: 'GO Name',
      subtitle: null,
      children: ['GO Details 3', 'Configuration 3', 'Settings 3'],
    ),
    AccordionItem(
      id: 'go_name_4',
      title: 'GO Name',
      subtitle: null,
      children: ['GO Details 4', 'Configuration 4', 'Settings 4'],
    ),
  ];

  @override
  void initState() {
    super.initState();
    _accordionController = AccordionController();
    _selectedIndustry = _industries.first;
    _filteredAccordionItems = _accordionItems;
    _searchController.addListener(_filterAccordionItems);
    _accordionController.addListener(() {
      setState(() {});
    });
  }

  void _filterAccordionItems() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      if (query.isEmpty) {
        _filteredAccordionItems = _accordionItems;
      } else {
        _filteredAccordionItems = _accordionItems.where((item) {
          return item.title.toLowerCase().contains(query) ||
                 (item.subtitle?.toLowerCase().contains(query) ?? false);
        }).toList();
      }
    });
  }

  @override
  void dispose() {
    _accordionController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Container(
          width: 534,
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Navigation Items
              // const HoverNavItems(),
              // Top Row with Select Industry and Search
              _buildTopRow(),
              
              // Accordion Items
              Flexible(
                child: Container(
                  constraints: BoxConstraints(maxHeight: 400),
                  child: SingleChildScrollView(
                    child: Column(
                      children: _filteredAccordionItems.map((item) => _buildAccordionItem(item)).toList(),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }


  Widget _buildTopRow() {
    return Container(
      padding: EdgeInsets.all(10),
      // decoration: BoxDecoration(
      //   border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      // ),
      child: Row(
        children: [
          // Select Industry Dropdown
          Expanded(
            flex: 2,
            child: Container(
              height: 40,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(4),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<String>(
                  value: _selectedIndustry,
                  isExpanded: true,
                  icon: Icon(Icons.keyboard_arrow_down, color: Colors.grey.shade600),
                   style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.titleMedium(context),
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.grey.shade700,
                    ),
                  items: _industries.map((String industry) {
                    return DropdownMenuItem<String>(
                      value: industry,
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: AppSpacing.xs),
                        child: Text(industry),
                      ),
                    );
                  }).toList(),
                  onChanged: (String? newValue) {
                    setState(() {
                      _selectedIndustry = newValue;
                    });
                  },
                ),
              ),
            ),
          ),
          
          SizedBox(width: AppSpacing.xs),
          
          // Search Field
          Expanded(
            flex: 2,
            child: Container(
              height: 40,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(4),
              ),
              child: TextField(
                controller: _searchController,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
                decoration: InputDecoration(
                  hintText: 'Search',
                  hintStyle: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodyMedium(context),
                    fontWeight: FontWeight.w400,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.grey.shade500,
                  ),
                  enabledBorder: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: AppSpacing.xs,
                    vertical: AppSpacing.xs,
                  ),
                  suffixIcon: Icon(
                    Icons.search,
                    color: Colors.grey.shade500,
                    size: 20,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccordionItem(AccordionItem item) {
    final isExpanded = _accordionController.isPanelExpanded(item.id);
    return Container(
      margin: EdgeInsets.only(top: 5, bottom: 5, left: 10, right: 10),
      decoration: BoxDecoration(
       border: Border.all(color: Colors.grey.shade300, width: 1),
        color: Colors.white,
      ),
      child: Column(
        children: [
          // Title row
          InkWell(
            onTap: () {
              _accordionController.togglePanel(item.id);
            },
            child: Container(
              padding: EdgeInsets.all(8),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      item.title,
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.titleMedium(context),
                        fontWeight: isExpanded ? FontWeight.w600 : FontWeight.w300,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  // Plus symbol without background
                  Padding(
                    padding: EdgeInsets.only(right: AppSpacing.xs),
                    child: Icon(
                      Icons.add,
                      color: Colors.grey.shade600,
                      size: 16,
                    ),
                  ),
                  // Arrow icon
                  AnimatedRotation(
                    turns: isExpanded ? 0.5 : 0.0,
                    duration: Duration(milliseconds: 200),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.grey.shade600,
                      size: 20,
                    ),
                  ),
                ],
              ),
            ),
          ),
          // Expandable content
          if (isExpanded) ...[
            Container(
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(color: Colors.grey.shade200, width: 1),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Show subtitle as content if available
                  if (item.subtitle != null) ...[
                    Container(
                      padding: EdgeInsets.all(6),
                      child: Text(
                        item.subtitle!,
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.bodyMedium(context),
                          fontWeight: FontWeight.w400,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ),
                  ] else ...[
                    // Show children items if no subtitle
                    ...item.children.map((child) => 
                      Container(
                        padding: EdgeInsets.all(6),
                        child: Row(
                          children: [
                            Container(
                              width: 4,
                              height: 4,
                              decoration: BoxDecoration(
                                color: Colors.grey.shade400,
                                shape: BoxShape.circle,
                              ),
                            ),
                            SizedBox(width: AppSpacing.xs),
                            Text(
                              child,
                              style: FontManager.getCustomStyle(
                                fontSize: ResponsiveFontSizes.bodyMedium(context),
                                fontWeight: FontWeight.w400,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.grey.shade700,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ).toList(),
                  ],
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}

class AccordionItem {
  final String id;
  final String title;
  final String? subtitle;
  final List<String> children;

  AccordionItem({
    required this.id,
    required this.title,
    this.subtitle,
    required this.children,
  });
}
