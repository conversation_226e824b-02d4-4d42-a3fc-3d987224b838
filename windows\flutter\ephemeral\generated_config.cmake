# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\SRC\\flutter_windows_3.29.3-stable\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\Downloads\\projects\\design-time-ui-flutter" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.4+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 4 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\SRC\\flutter_windows_3.29.3-stable\\flutter"
  "PROJECT_DIR=C:\\Users\\<USER>\\Downloads\\projects\\design-time-ui-flutter"
  "FLUTTER_ROOT=C:\\SRC\\flutter_windows_3.29.3-stable\\flutter"
  "FLUTTER_EPHEMERAL_DIR=C:\\Users\\<USER>\\Downloads\\projects\\design-time-ui-flutter\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=C:\\Users\\<USER>\\Downloads\\projects\\design-time-ui-flutter"
  "FLUTTER_TARGET=C:\\Users\\<USER>\\Downloads\\projects\\design-time-ui-flutter\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=C:\\Users\\<USER>\\Downloads\\projects\\design-time-ui-flutter\\.dart_tool\\package_config.json"
)
