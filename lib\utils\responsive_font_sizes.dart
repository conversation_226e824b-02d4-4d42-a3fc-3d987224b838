// responsive_font_sizes.dart
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'dart:math' as math;

/// A comprehensive class for handling responsive font sizes across different devices
/// Supports multiple responsive strategies and follows Material Design typography scale
class ResponsiveFontSizes {
  // Base breakpoints for responsive design
  static const double _mobileBreakpoint = 600;
  static const double _tabletBreakpoint = 1024;
  static const double _desktopBreakpoint = 1440;
  static const double _largeDesktopBreakpoint = 1920;
  static const double _extraLargeDesktopBreakpoint = 2560;
  static const double _ultraWideBreakpoint = 3440;
  static const double _superUltraWideBreakpoint = 3840;

  // Base font scale factors for different devices
  static const double _mobileScale = 0.85;
  static const double _tabletScale = 1.0;
  static const double _desktopScale = 1.15;
  static const double _largeDesktopScale = 1.3;
  static const double _extraLargeDesktopScale = 1.45;
  static const double _ultraWideScale = 1.6;
  static const double _superUltraWideScale = 1.75;

  /// Get device type based on screen width, platform, and pixel density
  static DeviceType _getDeviceType(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final width = size.width;
    final height = size.height;
    final pixelRatio = MediaQuery.of(context).devicePixelRatio;
    final physicalWidth = width * pixelRatio;
    final physicalHeight = height * pixelRatio;

    // Check for mobile devices first (regardless of platform)
    if (width < _mobileBreakpoint) {
      return DeviceType.mobile;
    }

    // Check for tablet range
    if (width < _tabletBreakpoint) {
      // For iPad Pro and similar large tablets
      if (pixelRatio > 2.0 && (width > 800 || height > 1000)) {
        return DeviceType.tabletLarge;
      }
      return DeviceType.tablet;
    }

    // For screens >= 1024px, determine based on width and platform
    if (width < _desktopBreakpoint) {
      // This covers small desktop/laptop screens and large tablets
      if (kIsWeb ||
          (!defaultTargetPlatform.name.contains('android') &&
              !defaultTargetPlatform.name.contains('ios'))) {
        return DeviceType.desktop;
      }
      return DeviceType.tabletLarge;
    }

    // Large screen categories
    if (width < _largeDesktopBreakpoint) {
      return DeviceType.desktop;
    }

    if (width < _extraLargeDesktopBreakpoint) {
      return DeviceType.desktopLarge;
    }

    if (width < _ultraWideBreakpoint) {
      return DeviceType.desktopExtraLarge;
    }

    if (width < _superUltraWideBreakpoint) {
      return DeviceType.ultraWide;
    }

    return DeviceType.superUltraWide;
  }

  /// Get responsive scale factor based on device type
  static double _getScaleFactor(BuildContext context) {
    final deviceType = _getDeviceType(context);
    switch (deviceType) {
      case DeviceType.mobile:
        return _mobileScale;
      case DeviceType.tablet:
        return _tabletScale;
      case DeviceType.tabletLarge:
        return _tabletScale * 1.1;
      case DeviceType.desktop:
        return _desktopScale;
      case DeviceType.desktopLarge:
        return _largeDesktopScale;
      case DeviceType.desktopExtraLarge:
        return _extraLargeDesktopScale;
      case DeviceType.ultraWide:
        return _ultraWideScale;
      case DeviceType.superUltraWide:
        return _superUltraWideScale;
    }
  }

  /// Get exact font size based on device type (with all screen sizes)
  static double _getExactSize(
    BuildContext context, {
    required double mobile,
    required double tablet,
    double? tabletLarge,
    required double desktop,
    double? desktopLarge,
    double? desktopExtraLarge,
    double? ultraWide,
    double? superUltraWide,
  }) {
    final deviceType = _getDeviceType(context);

    switch (deviceType) {
      case DeviceType.mobile:
        return mobile;
      case DeviceType.tablet:
        return tablet;
      case DeviceType.tabletLarge:
        return tabletLarge ?? (tablet * 1.1);
      case DeviceType.desktop:
        return desktop;
      case DeviceType.desktopLarge:
        return desktopLarge ?? (desktop * 1.15);
      case DeviceType.desktopExtraLarge:
        return desktopExtraLarge ?? (desktop * 1.3);
      case DeviceType.ultraWide:
        return ultraWide ?? (desktop * 1.45);
      case DeviceType.superUltraWide:
        return superUltraWide ?? (desktop * 1.6);
    }
  }

  /// Apply text scale factor from accessibility settings with intelligent clamping
  static double _applyTextScaleFactor(BuildContext context, double fontSize) {
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;
    final deviceType = _getDeviceType(context);

    // Different clamping ranges based on device type
    double minScale, maxScale;
    switch (deviceType) {
      case DeviceType.mobile:
        minScale = 0.8;
        maxScale = 2.5; // Allow more scaling on mobile for accessibility
        break;
      case DeviceType.tablet:
      case DeviceType.tabletLarge:
        minScale = 0.85;
        maxScale = 2.0;
        break;
      default: // Desktop and larger
        minScale = 0.9;
        maxScale = 1.5; // Less scaling needed on desktop
        break;
    }

    final clampedScaleFactor = textScaleFactor.clamp(minScale, maxScale);
    return fontSize * clampedScaleFactor;
  }

  /// Get font size with DPI awareness
  static double _getDPIAwareSize(BuildContext context, double baseSize) {
    final pixelRatio = MediaQuery.of(context).devicePixelRatio;
    final width = MediaQuery.of(context).size.width;

    // Adjust for high DPI displays
    if (pixelRatio > 2.0 && width > _desktopBreakpoint) {
      // High DPI desktop - slightly reduce to prevent oversized text
      return baseSize * 0.95;
    } else if (pixelRatio > 3.0 && width < _tabletBreakpoint) {
      // Very high DPI mobile - slightly increase for readability
      return baseSize * 1.05;
    }

    return baseSize;
  }

  // ===============================
  // MATERIAL DESIGN DISPLAY STYLES
  // ===============================

  /// Display Large - Largest text on screen, short and important text
  static double displayLarge(BuildContext context,
      {bool applyTextScale = true}) {
    final fontSize = _getExactSize(
      context,
      mobile: 40,
      tablet: 48,
      tabletLarge: 52,
      desktop: 57,
      desktopLarge: 64,
      desktopExtraLarge: 72,
      ultraWide: 80,
      superUltraWide: 88,
    );

    final dpiAwareSize = _getDPIAwareSize(context, fontSize);
    return applyTextScale
        ? _applyTextScaleFactor(context, dpiAwareSize)
        : dpiAwareSize;
  }

  /// Display Medium - High-emphasis text that's shorter than headlines
  static double displayMedium(BuildContext context,
      {bool applyTextScale = true}) {
    final fontSize = _getExactSize(
      context,
      mobile: 32,
      tablet: 40,
      tabletLarge: 44,
      desktop: 45,
      desktopLarge: 52,
      desktopExtraLarge: 58,
      ultraWide: 64,
      superUltraWide: 70,
    );

    final dpiAwareSize = _getDPIAwareSize(context, fontSize);
    return applyTextScale
        ? _applyTextScaleFactor(context, dpiAwareSize)
        : dpiAwareSize;
  }

  /// Display Small - Medium-emphasis text that's shorter than headlines
  static double displaySmall(BuildContext context,
      {bool applyTextScale = true}) {
    final fontSize = _getExactSize(
      context,
      mobile: 28,
      tablet: 32,
      tabletLarge: 35,
      desktop: 36,
      desktopLarge: 40,
      desktopExtraLarge: 44,
      ultraWide: 48,
      superUltraWide: 52,
    );

    final dpiAwareSize = _getDPIAwareSize(context, fontSize);
    return applyTextScale
        ? _applyTextScaleFactor(context, dpiAwareSize)
        : dpiAwareSize;
  }

  // ===============================
  // MATERIAL DESIGN HEADLINE STYLES
  // ===============================

  /// Headline Large - High-emphasis text for main headings
  static double headlineLarge(BuildContext context,
      {bool applyTextScale = true}) {
    final fontSize = _getExactSize(
      context,
      mobile: 28,
      tablet: 34,
      tabletLarge: 34,
      desktop: 34,
      desktopLarge: 36,
      desktopExtraLarge: 38,
      ultraWide: 40,
      superUltraWide: 42,
    );

    final dpiAwareSize = _getDPIAwareSize(context, fontSize);
    return applyTextScale
        ? _applyTextScaleFactor(context, dpiAwareSize)
        : dpiAwareSize;
  }

  /// Headline Medium - High-emphasis text for section headings
  static double headlineMedium(BuildContext context,
      {bool applyTextScale = true}) {
    final fontSize = _getExactSize(
      context,
      mobile: 24,
      tablet: 28,
      tabletLarge: 28,
      desktop: 28,
      desktopLarge: 32,
      desktopExtraLarge: 34,
      ultraWide: 36,
      superUltraWide: 38,
    );

    final dpiAwareSize = _getDPIAwareSize(context, fontSize);
    return applyTextScale
        ? _applyTextScaleFactor(context, dpiAwareSize)
        : dpiAwareSize;
  }

  /// Headline Small - High-emphasis text for subsection headings
  static double headlineSmall(BuildContext context,
      {bool applyTextScale = true}) {
    final fontSize = _getExactSize(
      context,
      mobile: 22,
      tablet: 24,
      tabletLarge: 24,
      desktop: 24,
      desktopLarge: 28,
      desktopExtraLarge: 30,
      ultraWide: 3,
      superUltraWide: 34,
    );

    final dpiAwareSize = _getDPIAwareSize(context, fontSize);
    return applyTextScale
        ? _applyTextScaleFactor(context, dpiAwareSize)
        : dpiAwareSize;
  }

  // ===============================
  // MATERIAL DESIGN TITLE STYLES
  // ===============================

  /// Title Large - Medium-emphasis text for important content
  static double titleLarge(BuildContext context, {bool applyTextScale = true}) {
    final fontSize = _getExactSize(
      context,
      mobile: 14,
      tablet: 14,
      tabletLarge: 16,
      desktop: 16,
      desktopLarge: 18,
      desktopExtraLarge: 20,
      ultraWide: 22,
      superUltraWide: 24,
    );

    final dpiAwareSize = _getDPIAwareSize(context, fontSize);
    return applyTextScale
        ? _applyTextScaleFactor(context, dpiAwareSize)
        : dpiAwareSize;
  }

  /// Title Medium - Medium-emphasis text for content hierarchy
  static double titleMedium(BuildContext context,
      {bool applyTextScale = true}) {
    final fontSize = _getExactSize(
      context,
      mobile: 10,
      tablet: 12,
      tabletLarge: 14,
      desktop: 14,
      desktopLarge: 16,
      desktopExtraLarge: 18,
      ultraWide: 24,
      superUltraWide: 26,
    );

    final dpiAwareSize = _getDPIAwareSize(context, fontSize);
    return applyTextScale
        ? _applyTextScaleFactor(context, dpiAwareSize)
        : dpiAwareSize;
  }

  /// Title Small - Medium-emphasis text for smaller content
  static double titleSmall(BuildContext context, {bool applyTextScale = true}) {
    final fontSize = _getExactSize(
      context,
      mobile: 10,
      tablet: 12,
      tabletLarge: 12,
      desktop: 12,
      desktopLarge: 14,
      desktopExtraLarge: 16,
      ultraWide: 18,
      superUltraWide: 20,
    );

    final dpiAwareSize = _getDPIAwareSize(context, fontSize);
    return applyTextScale
        ? _applyTextScaleFactor(context, dpiAwareSize)
        : dpiAwareSize;
  }

  // ===============================
  // MATERIAL DESIGN BODY STYLES
  // ===============================

  /// Body Large - Emphasized body text for main content
  static double bodyLarge(BuildContext context, {bool applyTextScale = true}) {
    final fontSize = _getExactSize(
      context,
      mobile: 16,
      tablet: 16,
      tabletLarge: 18,
      desktop: 18,
      desktopLarge: 20,
      desktopExtraLarge: 22,
      ultraWide: 24,
      superUltraWide: 26,
    );

    final dpiAwareSize = _getDPIAwareSize(context, fontSize);
    return applyTextScale
        ? _applyTextScaleFactor(context, dpiAwareSize)
        : dpiAwareSize;
  }

  /// Body Medium - Regular body text for main content
  static double bodyMedium(BuildContext context, {bool applyTextScale = true}) {
    final fontSize = _getExactSize(
      context,
      mobile: 12,
      tablet: 14,
      tabletLarge: 14,
      desktop: 14,
      desktopLarge: 16,
      desktopExtraLarge: 18,
      ultraWide: 20,
      superUltraWide: 22,
    );

    final dpiAwareSize = _getDPIAwareSize(context, fontSize);
    return applyTextScale
        ? _applyTextScaleFactor(context, dpiAwareSize)
        : dpiAwareSize;
  }

  /// Body Small - Lower-emphasis body text
  static double bodySmall(BuildContext context, {bool applyTextScale = true}) {
    final fontSize = _getExactSize(
      context,
      mobile: 10,
      tablet: 12,
      tabletLarge: 12,
      desktop: 12,
      desktopLarge: 14,
      desktopExtraLarge: 16,
      ultraWide: 18,
      superUltraWide: 20,
    );

    final dpiAwareSize = _getDPIAwareSize(context, fontSize);
    return applyTextScale
        ? _applyTextScaleFactor(context, dpiAwareSize)
        : dpiAwareSize;
  }

  // ===============================
  // MATERIAL DESIGN LABEL STYLES
  // ===============================

  /// Label Large - Call-to-action text on buttons
  static double labelLarge(BuildContext context, {bool applyTextScale = true}) {
    final fontSize = _getExactSize(
      context,
      mobile: 12,
      tablet: 14,
      tabletLarge: 14,
      desktop: 14,
      desktopLarge: 16,
      desktopExtraLarge: 18,
      ultraWide: 20,
      superUltraWide: 22,
    );

    final dpiAwareSize = _getDPIAwareSize(context, fontSize);
    return applyTextScale
        ? _applyTextScaleFactor(context, dpiAwareSize)
        : dpiAwareSize;
  }

  /// Label Medium - Text on smaller buttons and links
  static double labelMedium(BuildContext context,
      {bool applyTextScale = true}) {
    final fontSize = _getExactSize(
      context,
      mobile: 11,
      tablet: 12,
      tabletLarge: 12,
      desktop: 12,
      desktopLarge: 14,
      desktopExtraLarge: 16,
      ultraWide: 18,
      superUltraWide: 20,
    );

    final dpiAwareSize = _getDPIAwareSize(context, fontSize);
    return applyTextScale
        ? _applyTextScaleFactor(context, dpiAwareSize)
        : dpiAwareSize;
  }

  /// Label Small - Text for small elements
  static double labelSmall(BuildContext context, {bool applyTextScale = true}) {
    final fontSize = _getExactSize(
      context,
      mobile: 9,
      tablet: 10,
      tabletLarge: 10,
      desktop: 11,
      desktopLarge: 12,
      desktopExtraLarge: 14,
      ultraWide: 16,
      superUltraWide: 18,
    );

    final dpiAwareSize = _getDPIAwareSize(context, fontSize);
    return applyTextScale
        ? _applyTextScaleFactor(context, dpiAwareSize)
        : dpiAwareSize;
  }

  // new responsive classes
  // ===============================
  // UTILITY METHODS
  // ===============================

  /// Custom responsive font size with manual breakpoint values
  static double custom(
    BuildContext context, {
    required double mobile,
    required double tablet,
    double? tabletLarge,
    required double desktop,
    double? desktopLarge,
    double? desktopExtraLarge,
    double? ultraWide,
    double? superUltraWide,
    bool applyTextScale = false,
  }) {
    final fontSize = _getExactSize(
      context,
      mobile: mobile,
      tablet: tablet,
      tabletLarge: tabletLarge,
      desktop: desktop,
      desktopLarge: desktopLarge,
      desktopExtraLarge: desktopExtraLarge,
      ultraWide: ultraWide,
      superUltraWide: superUltraWide,
    );

    final dpiAwareSize = _getDPIAwareSize(context, fontSize);
    return applyTextScale
        ? _applyTextScaleFactor(context, dpiAwareSize)
        : dpiAwareSize;
  }

  /// Scale any font size based on current device
  static double scale(BuildContext context, double baseSize,
      {bool applyTextScale = true}) {
    final scaledSize = baseSize * _getScaleFactor(context);
    final dpiAwareSize = _getDPIAwareSize(context, scaledSize);
    return applyTextScale
        ? _applyTextScaleFactor(context, dpiAwareSize)
        : dpiAwareSize;
  }

  /// Get font size based on viewport width percentage with better clamping
  static double viewport(
    BuildContext context,
    double percentage, {
    double? minSize,
    double? maxSize,
    bool applyTextScale = true,
  }) {
    final width = MediaQuery.of(context).size.width;
    final deviceType = _getDeviceType(context);

    double fontSize = width * (percentage / 100);

    // Apply device-specific defaults if not provided
    if (minSize == null) {
      switch (deviceType) {
        case DeviceType.mobile:
          minSize = 10;
          break;
        case DeviceType.tablet:
        case DeviceType.tabletLarge:
          minSize = 12;
          break;
        default:
          minSize = 14;
          break;
      }
    }

    if (maxSize == null) {
      switch (deviceType) {
        case DeviceType.mobile:
          maxSize = 32;
          break;
        case DeviceType.tablet:
        case DeviceType.tabletLarge:
          maxSize = 48;
          break;
        default:
          maxSize = 72;
          break;
      }
    }

    fontSize = fontSize.clamp(minSize, maxSize);
    final dpiAwareSize = _getDPIAwareSize(context, fontSize);
    return applyTextScale
        ? _applyTextScaleFactor(context, dpiAwareSize)
        : dpiAwareSize;
  }

  /// Get font size for specific device type
  static double forDevice(
    BuildContext context,
    DeviceType deviceType,
    double baseSize, {
    bool applyTextScale = true,
  }) {
    double scaleFactor;
    switch (deviceType) {
      case DeviceType.mobile:
        scaleFactor = _mobileScale;
        break;
      case DeviceType.tablet:
        scaleFactor = _tabletScale;
        break;
      case DeviceType.tabletLarge:
        scaleFactor = _tabletScale * 1.1;
        break;
      case DeviceType.desktop:
        scaleFactor = _desktopScale;
        break;
      case DeviceType.desktopLarge:
        scaleFactor = _largeDesktopScale;
        break;
      case DeviceType.desktopExtraLarge:
        scaleFactor = _extraLargeDesktopScale;
        break;
      case DeviceType.ultraWide:
        scaleFactor = _ultraWideScale;
        break;
      case DeviceType.superUltraWide:
        scaleFactor = _superUltraWideScale;
        break;
    }

    final fontSize = baseSize * scaleFactor;
    final dpiAwareSize = _getDPIAwareSize(context, fontSize);
    return applyTextScale
        ? _applyTextScaleFactor(context, dpiAwareSize)
        : dpiAwareSize;
  }

  // ===============================
  // THEME INTEGRATION
  // ===============================

  /// Generate a complete responsive TextTheme
  static TextTheme getResponsiveTextTheme(
    BuildContext context, {
    String? fontFamily,
    Color? color,
  }) {
    return TextTheme(
      displayLarge: TextStyle(
        fontSize: displayLarge(context),
        fontWeight: FontWeight.w300,
        letterSpacing: -1.5,
        fontFamily: fontFamily,
        color: color,
        height: getLineHeight(displayLarge(context, applyTextScale: false)),
      ),
      displayMedium: TextStyle(
        fontSize: displayMedium(context),
        fontWeight: FontWeight.w300,
        letterSpacing: -0.5,
        fontFamily: fontFamily,
        color: color,
        height: getLineHeight(displayMedium(context, applyTextScale: false)),
      ),
      displaySmall: TextStyle(
        fontSize: displaySmall(context),
        fontWeight: FontWeight.w400,
        letterSpacing: 0.0,
        fontFamily: fontFamily,
        color: color,
        height: getLineHeight(displaySmall(context, applyTextScale: false)),
      ),
      headlineLarge: TextStyle(
        fontSize: headlineLarge(context),
        fontWeight: FontWeight.w400,
        letterSpacing: 0.25,
        fontFamily: fontFamily,
        color: color,
        height: getLineHeight(headlineLarge(context, applyTextScale: false)),
      ),
      headlineMedium: TextStyle(
        fontSize: headlineMedium(context),
        fontWeight: FontWeight.w400,
        letterSpacing: 0.0,
        fontFamily: fontFamily,
        color: color,
        height: getLineHeight(headlineMedium(context, applyTextScale: false)),
      ),
      headlineSmall: TextStyle(
        fontSize: headlineSmall(context),
        fontWeight: FontWeight.w400,
        letterSpacing: 0.0,
        fontFamily: fontFamily,
        color: color,
        height: getLineHeight(headlineSmall(context, applyTextScale: false)),
      ),
      titleLarge: TextStyle(
        fontSize: titleLarge(context),
        fontWeight: FontWeight.w500,
        letterSpacing: 0.15,
        fontFamily: fontFamily,
        color: color,
        height: getLineHeight(titleLarge(context, applyTextScale: false)),
      ),
      titleMedium: TextStyle(
        fontSize: titleMedium(context),
        fontWeight: FontWeight.w500,
        letterSpacing: 0.15,
        fontFamily: fontFamily,
        color: color,
        height: getLineHeight(titleMedium(context, applyTextScale: false)),
      ),
      titleSmall: TextStyle(
        fontSize: titleSmall(context),
        fontWeight: FontWeight.w500,
        letterSpacing: 0.1,
        fontFamily: fontFamily,
        color: color,
        height: getLineHeight(titleSmall(context, applyTextScale: false)),
      ),
      bodyLarge: TextStyle(
        fontSize: bodyLarge(context),
        fontWeight: FontWeight.w400,
        letterSpacing: 0.5,
        fontFamily: fontFamily,
        color: color,
        height: getLineHeight(bodyLarge(context, applyTextScale: false)),
      ),
      bodyMedium: TextStyle(
        fontSize: bodyMedium(context),
        fontWeight: FontWeight.w400,
        letterSpacing: 0.25,
        fontFamily: fontFamily,
        color: color,
        height: getLineHeight(bodyMedium(context, applyTextScale: false)),
      ),
      bodySmall: TextStyle(
        fontSize: bodySmall(context),
        fontWeight: FontWeight.w400,
        letterSpacing: 0.4,
        fontFamily: fontFamily,
        color: color,
        height: getLineHeight(bodySmall(context, applyTextScale: false)),
      ),
      labelLarge: TextStyle(
        fontSize: labelLarge(context),
        fontWeight: FontWeight.w500,
        letterSpacing: 1.25,
        fontFamily: fontFamily,
        color: color,
        height: getLineHeight(labelLarge(context, applyTextScale: false)),
      ),
      labelMedium: TextStyle(
        fontSize: labelMedium(context),
        fontWeight: FontWeight.w500,
        letterSpacing: 1.25,
        fontFamily: fontFamily,
        color: color,
        height: getLineHeight(labelMedium(context, applyTextScale: false)),
      ),
      labelSmall: TextStyle(
        fontSize: labelSmall(context),
        fontWeight: FontWeight.w500,
        letterSpacing: 1.5,
        fontFamily: fontFamily,
        color: color,
        height: getLineHeight(labelSmall(context, applyTextScale: false)),
      ),
    );
  }

  // ===============================
  // HELPER METHODS
  // ===============================

  /// Get appropriate line height for given font size with device awareness
  static double getLineHeight(double fontSize) {
    if (fontSize >= 48) return 1.1;
    if (fontSize >= 32) return 1.2;
    if (fontSize >= 24) return 1.3;
    if (fontSize >= 16) return 1.4;
    if (fontSize >= 14) return 1.5;
    return 1.6;
  }

  /// Get appropriate letter spacing for given font size
  static double getLetterSpacing(double fontSize) {
    if (fontSize >= 48) return -1.0;
    if (fontSize >= 32) return -0.5;
    if (fontSize >= 24) return 0.0;
    if (fontSize >= 16) return 0.15;
    if (fontSize >= 14) return 0.25;
    return 0.4;
  }

  /// Get appropriate font weight for emphasis level
  static FontWeight getFontWeight(EmphasisLevel emphasis) {
    switch (emphasis) {
      case EmphasisLevel.high:
        return FontWeight.w700;
      case EmphasisLevel.medium:
        return FontWeight.w500;
      case EmphasisLevel.low:
        return FontWeight.w400;
      case EmphasisLevel.disabled:
        return FontWeight.w300;
    }
  }

  /// Get current device type
  static DeviceType getDeviceType(BuildContext context) {
    return _getDeviceType(context);
  }

  /// Get screen size category as string
  static String getScreenSizeCategory(BuildContext context) {
    final deviceType = _getDeviceType(context);
    final width = MediaQuery.of(context).size.width;

    return '${deviceType.name} (${width.toInt()}px)';
  }

  /// Check if current device is mobile
  static bool isMobile(BuildContext context) {
    return _getDeviceType(context) == DeviceType.mobile;
  }

  /// Check if current device is tablet
  static bool isTablet(BuildContext context) {
    final deviceType = _getDeviceType(context);
    return deviceType == DeviceType.tablet ||
        deviceType == DeviceType.tabletLarge;
  }

  /// Check if current device is desktop
  static bool isDesktop(BuildContext context) {
    final deviceType = _getDeviceType(context);
    return [
      DeviceType.desktop,
      DeviceType.desktopLarge,
      DeviceType.desktopExtraLarge,
      DeviceType.ultraWide,
      DeviceType.superUltraWide,
    ].contains(deviceType);
  }

  /// Check if current device is large screen (desktop+)
  static bool isLargeScreen(BuildContext context) {
    final deviceType = _getDeviceType(context);
    return [
      DeviceType.desktopLarge,
      DeviceType.desktopExtraLarge,
      DeviceType.ultraWide,
      DeviceType.superUltraWide,
    ].contains(deviceType);
  }

  /// Check if running on web platform
  static bool get isWebPlatform => kIsWeb;

  /// Get responsive padding based on screen size
  static EdgeInsets getResponsivePadding(
    BuildContext context, {
    double? mobile,
    double? tablet,
    double? desktop,
  }) {
    final deviceType = _getDeviceType(context);
    double padding;

    switch (deviceType) {
      case DeviceType.mobile:
        padding = mobile ?? 16.0;
        break;
      case DeviceType.tablet:
      case DeviceType.tabletLarge:
        padding = tablet ?? 24.0;
        break;
      default:
        padding = desktop ?? 32.0;
        break;
    }

    return EdgeInsets.all(padding);
  }

  /// Get responsive margin based on screen size
  static EdgeInsets getResponsiveMargin(
    BuildContext context, {
    double? mobile,
    double? tablet,
    double? desktop,
  }) {
    final deviceType = _getDeviceType(context);
    double margin;

    switch (deviceType) {
      case DeviceType.mobile:
        margin = mobile ?? 8.0;
        break;
      case DeviceType.tablet:
      case DeviceType.tabletLarge:
        margin = tablet ?? 16.0;
        break;
      default:
        margin = desktop ?? 24.0;
        break;
    }

    return EdgeInsets.all(margin);
  }
}

// ===============================
// ENUMS AND SUPPORTING CLASSES
// ===============================

enum DeviceType {
  mobile,
  tablet,
  tabletLarge,
  desktop,
  desktopLarge,
  desktopExtraLarge,
  ultraWide,
  superUltraWide,
}

enum EmphasisLevel { high, medium, low, disabled }

/// Extension methods for DeviceType enum
extension DeviceTypeExtension on DeviceType {
  String get name {
    switch (this) {
      case DeviceType.mobile:
        return 'Mobile';
      case DeviceType.tablet:
        return 'Tablet';
      case DeviceType.tabletLarge:
        return 'Large Tablet';
      case DeviceType.desktop:
        return 'Desktop';
      case DeviceType.desktopLarge:
        return 'Large Desktop';
      case DeviceType.desktopExtraLarge:
        return 'Extra Large Desktop';
      case DeviceType.ultraWide:
        return 'Ultra Wide';
      case DeviceType.superUltraWide:
        return 'Super Ultra Wide';
    }
  }

  bool get isMobile => this == DeviceType.mobile;
  bool get isTablet =>
      this == DeviceType.tablet || this == DeviceType.tabletLarge;
  bool get isDesktop => [
        DeviceType.desktop,
        DeviceType.desktopLarge,
        DeviceType.desktopExtraLarge,
        DeviceType.ultraWide,
        DeviceType.superUltraWide,
      ].contains(this);
  bool get isLargeScreen => [
        DeviceType.desktopLarge,
        DeviceType.desktopExtraLarge,
        DeviceType.ultraWide,
        DeviceType.superUltraWide,
      ].contains(this);
}

/// Responsive Text Style Builder
class ResponsiveTextStyle {
  final BuildContext context;

  ResponsiveTextStyle._(this.context);

  factory ResponsiveTextStyle.of(BuildContext context) {
    return ResponsiveTextStyle._(context);
  }

  TextStyle get displayLarge => TextStyle(
        fontSize: ResponsiveFontSizes.displayLarge(context),
        fontWeight: FontWeight.w300,
        height: ResponsiveFontSizes.getLineHeight(
            ResponsiveFontSizes.displayLarge(context, applyTextScale: false)),
      );

  TextStyle get displayMedium => TextStyle(
        fontSize: ResponsiveFontSizes.displayMedium(context),
        fontWeight: FontWeight.w300,
        height: ResponsiveFontSizes.getLineHeight(
            ResponsiveFontSizes.displayMedium(context, applyTextScale: false)),
      );

  TextStyle get displaySmall => TextStyle(
        fontSize: ResponsiveFontSizes.displaySmall(context),
        fontWeight: FontWeight.w400,
        height: ResponsiveFontSizes.getLineHeight(
            ResponsiveFontSizes.displaySmall(context, applyTextScale: false)),
      );

  TextStyle get headlineLarge => TextStyle(
        fontSize: ResponsiveFontSizes.headlineLarge(context),
        fontWeight: FontWeight.w400,
        height: ResponsiveFontSizes.getLineHeight(
            ResponsiveFontSizes.headlineLarge(context, applyTextScale: false)),
      );

  TextStyle get headlineMedium => TextStyle(
        fontSize: ResponsiveFontSizes.headlineMedium(context),
        fontWeight: FontWeight.w400,
        height: ResponsiveFontSizes.getLineHeight(
            ResponsiveFontSizes.headlineMedium(context, applyTextScale: false)),
      );

  TextStyle get headlineSmall => TextStyle(
        fontSize: ResponsiveFontSizes.headlineSmall(context),
        fontWeight: FontWeight.w400,
        height: ResponsiveFontSizes.getLineHeight(
            ResponsiveFontSizes.headlineSmall(context, applyTextScale: false)),
      );

  TextStyle get titleLarge => TextStyle(
        fontSize: ResponsiveFontSizes.titleLarge(context),
        fontWeight: FontWeight.w500,
        height: ResponsiveFontSizes.getLineHeight(
            ResponsiveFontSizes.titleLarge(context, applyTextScale: false)),
      );

  TextStyle get titleMedium => TextStyle(
        fontSize: ResponsiveFontSizes.titleMedium(context),
        fontWeight: FontWeight.w500,
        height: ResponsiveFontSizes.getLineHeight(
            ResponsiveFontSizes.titleMedium(context, applyTextScale: false)),
      );

  TextStyle get titleSmall => TextStyle(
        fontSize: ResponsiveFontSizes.titleSmall(context),
        fontWeight: FontWeight.w500,
        height: ResponsiveFontSizes.getLineHeight(
            ResponsiveFontSizes.titleSmall(context, applyTextScale: false)),
      );

  TextStyle get bodyLarge => TextStyle(
        fontSize: ResponsiveFontSizes.bodyLarge(context),
        fontWeight: FontWeight.w400,
        height: ResponsiveFontSizes.getLineHeight(
            ResponsiveFontSizes.bodyLarge(context, applyTextScale: false)),
      );

  TextStyle get bodyMedium => TextStyle(
        fontSize: ResponsiveFontSizes.bodyMedium(context),
        fontWeight: FontWeight.w400,
        height: ResponsiveFontSizes.getLineHeight(
            ResponsiveFontSizes.bodyMedium(context, applyTextScale: false)),
      );

  TextStyle get bodySmall => TextStyle(
        fontSize: ResponsiveFontSizes.bodySmall(context),
        fontWeight: FontWeight.w400,
        height: ResponsiveFontSizes.getLineHeight(
            ResponsiveFontSizes.bodySmall(context, applyTextScale: false)),
      );

  TextStyle get labelLarge => TextStyle(
        fontSize: ResponsiveFontSizes.labelLarge(context),
        fontWeight: FontWeight.w500,
        height: ResponsiveFontSizes.getLineHeight(
            ResponsiveFontSizes.labelLarge(context, applyTextScale: false)),
      );

  TextStyle get labelMedium => TextStyle(
        fontSize: ResponsiveFontSizes.labelMedium(context),
        fontWeight: FontWeight.w500,
        height: ResponsiveFontSizes.getLineHeight(
            ResponsiveFontSizes.labelMedium(context, applyTextScale: false)),
      );

  TextStyle get labelSmall => TextStyle(
        fontSize: ResponsiveFontSizes.labelSmall(context),
        fontWeight: FontWeight.w500,
        height: ResponsiveFontSizes.getLineHeight(
            ResponsiveFontSizes.labelSmall(context, applyTextScale: false)),
      );

  /// Custom style with responsive sizing
  TextStyle custom({
    required double mobile,
    required double tablet,
    double? tabletLarge,
    required double desktop,
    double? desktopLarge,
    double? desktopExtraLarge,
    double? ultraWide,
    double? superUltraWide,
    FontWeight? fontWeight,
    Color? color,
    double? letterSpacing,
    double? height,
  }) {
    final fontSize = ResponsiveFontSizes.custom(
      context,
      mobile: mobile,
      tablet: tablet,
      tabletLarge: tabletLarge,
      desktop: desktop,
      desktopLarge: desktopLarge,
      desktopExtraLarge: desktopExtraLarge,
      ultraWide: ultraWide,
      superUltraWide: superUltraWide,
    );

    return TextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight ?? FontWeight.w400,
      color: color,
      letterSpacing:
          letterSpacing ?? ResponsiveFontSizes.getLetterSpacing(fontSize),
      height: height ?? ResponsiveFontSizes.getLineHeight(fontSize),
    );
  }
}
