import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/models/workflow/entity_manual_response_model.dart';
import 'package:nsl/providers/web_home_provider_static.dart';
import 'package:nsl/screens/web/new_design/widgets/agent_hierarchy_chart.dart';
import 'package:nsl/screens/web/new_design/widgets/book_solution_app_bar.dart';
import 'package:nsl/screens/web/new_design/widgets/validation_widgets/agent_table.dart';
import 'package:nsl/screens/web/new_design/widgets/validation_widgets/entity_details_panel.dart';
import 'package:nsl/screens/web/new_design/widgets/validation_widgets/entity_table.dart';
import 'package:nsl/screens/web/new_design/widgets/validation_widgets/lo_entry_screen.dart';
import 'package:nsl/screens/web/new_design/widgets/validation_widgets/role_details_panel.dart';
import 'package:nsl/screens/web/new_design/widgets/validation_widgets/workflow_lo_details_panel.dart';
import 'package:nsl/screens/web/new_design/widgets/validation_widgets/workflow_lo_with_workflow_structure.dart';
import 'package:nsl/screens/web/new_design/widgets/validation_widgets/workflow_table.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/logger.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';
import '../../../models/conversation_response.dart';
import '../../../providers/manual_creation_provider.dart';
import '../../../widgets/resizable_panel.dart';
import '../../../widgets/breadcrumb_widget.dart';

class ManualCreationStaticScreen extends StatelessWidget {
  const ManualCreationStaticScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return _ManualCreationStaticScreenContent();
  }
}

class _ManualCreationStaticScreenContent extends StatefulWidget {
  @override
  _ManualCreationStaticScreenContentState createState() =>
      _ManualCreationStaticScreenContentState();
}

class _ManualCreationStaticScreenContentState
    extends State<_ManualCreationStaticScreenContent> {
  String? _lastShownError;
  String? _lastShownResult;
  // String? _activeIconView = "Agents"; // Track which icon view is active

  int selectedSectionIndex = 0;
  bool _isAIEnabled = true; // Add AI toggle state
  bool _isChatEnabled = false; // Add chat toggle state

  Map<String, EntityElement> globalEntityElements = {};

  @override
  Widget build(BuildContext context) {
    Provider.of<ManualCreationProvider>(context, listen: false).intializeWidths(
        MediaQuery.of(context).size.width / 2,
        MediaQuery.of(context).size.width / 6,
        MediaQuery.of(context).size.width / 2);

    // Set the current screen index to manual creation static screen
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<WebHomeProviderStatic>(context, listen: false)
          .setManualCreationStaticScreen();
    });

    return Consumer<ManualCreationProvider>(
      builder: (context, provider, child) {
        // Show alert dialog for validation results
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _showValidationAlertDialog(context, provider);
        });

        return _buildMainContent(context, provider);
      },
    );
  }

  Widget _buildMainContent(
      BuildContext context, ManualCreationProvider provider) {
    return Scaffold(
      // backgroundColor: Colors.white,
      // appBar: BookSolutionAppBar(
      //   projectName: 'Project Name',
      //   showChatToggle: true,
      //   showAIToggle: true,
      //   isChatEnabled: _isChatEnabled,
      //   isAIEnabled: _isAIEnabled,
      //   onChatToggle: () {
      //     setState(() {
      //       _isChatEnabled = !_isChatEnabled;
      //     });
      //   },
      //   onAIToggle: () {
      //     setState(() {
      //       _isAIEnabled = !_isAIEnabled;
      //     });
      //   },
      // ),
      body: Row(
        children: [
          Expanded(
            child: Row(
              children: [
                (provider.showSidePanel ||
                        provider.showWorkflowLoSidePanel ||
                        provider.showSideEntityPanel)
                    ? SizedBox(
                        width: MediaQuery.of(context).size.width >= 1900
                            ? AppSpacing.spaceBetweenMainStatic
                            : AppSpacing.spaceBetweenMainStatic,
                        child: rightSideTooltips(provider),
                      )
                    : Expanded(
                        child: rightSideTooltips(provider),
                      ),

                // Main content area
                Expanded(
                  flex: provider.showSidePanel ? 3 : 7,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: AppSpacing.sm),
                      // Header with dynamic breadcrumb
                      GestureDetector(
                        onTap: () {
                          final webHomeProvider =
                              Provider.of<WebHomeProviderStatic>(context,
                                  listen: false);
                          webHomeProvider.currentScreenIndex =
                              ScreenConstants.home;
                          // Don't reset conversation when navigating from manual creation screen
                          // The provider will handle this check internally
                        },
                        child: MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: BreadcrumbWidget(
                            currentStep: provider.currentStep,
                            onHomeTap: () {
                              final webHomeProvider =
                                  Provider.of<WebHomeProviderStatic>(context,
                                      listen: false);
                              webHomeProvider.currentScreenIndex =
                                  ScreenConstants.home;
                              // Don't reset conversation when navigating from manual creation screen
                              // The provider will handle this check internally
                            },
                          ),
                        ),
                      ),

                      SizedBox(height: 24),

                      // Title
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            _getTitle(provider),
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              fontFamily: 'TiemposText',
                              color: Colors.black,
                            ),
                          ),
                          Row(
                            children: [
                              MouseRegion(
                                cursor: SystemMouseCursors.click,
                                child: GestureDetector(
                                  onTap: () {
                                    switch (provider.currentStep) {
                                      case WorkflowStep.agentCreation:
                                        provider.clearAgentSection();
                                        break;
                                      case WorkflowStep.entityCreation:
                                        provider.clearEntitySection();
                                        break;
                                      case WorkflowStep.workflowCreation:
                                        provider.clearWorkflowSection();
                                        break;
                                      case WorkflowStep.workflowLoCreation:
                                        provider.clearWorkflowLoSection();
                                        break;
                                    }
                                  },
                                  child: Text(
                                    "Clear",
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      fontFamily: 'TiemposText',
                                      color: Color(0xff0058FF),
                                    ),
                                  ),
                                ),
                              ),
                              // Add icon - only show after getting responses
                              if (_shouldShowAddIcon(provider)) ...[
                                SizedBox(width: 16),
                                MouseRegion(
                                  cursor: SystemMouseCursors.click,
                                  child: GestureDetector(
                                    onTap: () {
                                      if (provider.currentStep ==
                                          WorkflowStep.agentCreation) {
                                        provider.toggleAgentView();
                                      }
                                    },
                                    child: Icon(
                                      Icons.font_download,
                                      size: 18,
                                      color: Color(0xff0058FF),
                                    ),
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ],
                      ),
                      SizedBox(height: 2),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 9),
                        decoration: BoxDecoration(
                          color: const Color(0xFFE9F2F7),
                          border: const Border(
                            left:
                                BorderSide(color: Color(0xffD0D0D0), width: 1),
                            right:
                                BorderSide(color: Color(0xffD0D0D0), width: 1),
                            top: BorderSide(color: Color(0xffD0D0D0), width: 1),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            MouseRegion(
                              cursor:
                                  SystemMouseCursors.click, // Pointer on hover
                              child: GestureDetector(
                                onTap: () {
                                  Provider.of<WebHomeProviderStatic>(context,
                                              listen: false)
                                          .currentScreenIndex =
                                      ScreenConstants.webMyLibrary;
                                },
                                child: Row(
                                  children: [
                                    const Icon(Icons.folder_copy,
                                        size: 14, color: Colors.black87),
                                    const SizedBox(width: 4),
                                    Text(
                                      'My Library',
                                      style: FontManager.getCustomStyle(
                                        fontSize:
                                            ResponsiveFontSizes.titleSmall(
                                                context),
                                        fontWeight: FontWeight.w500,
                                        fontFamily:
                                            FontManager.fontFamilyTiemposText,
                                        color: Colors.black87,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            MouseRegion(
                              cursor:
                                  SystemMouseCursors.click, // Pointer on hover
                              child: GestureDetector(
                                onTap: () {
                                  Provider.of<WebHomeProviderStatic>(context,
                                              listen: false)
                                          .currentScreenIndex =
                                      ScreenConstants.webMyLibrary;
                                },
                                child: Row(
                                  children: [
                                    const Icon(Icons.language,
                                        size: 14, color: Colors.black87),
                                    const SizedBox(width: 4),
                                    Text(
                                      'Global Library',
                                      style: FontManager.getCustomStyle(
                                        fontSize:
                                            ResponsiveFontSizes.titleSmall(
                                                context),
                                        fontWeight: FontWeight.w500,
                                        fontFamily:
                                            FontManager.fontFamilyTiemposText,
                                        color: Colors.black87,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            )
                          ],
                        ),
                      ),

                      // Main content area with icons and text field
                      Expanded(
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Left side - Icons column

                            // SizedBox(width: 16),

                            // Right side - Large text field or table
                            Expanded(
                              child: _buildWorkflowContent(context, provider),
                            ),
                          ],
                        ),
                      ),

                      SizedBox(height: 8),

                      // Bottom buttons
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          //Cancel button
                          TextButton(
                            onPressed: () {
                              // Navigator.of(context).pop();
                            },
                            style: TextButton.styleFrom(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 30, vertical: 10),
                              side: BorderSide(
                                  color: Color(0xffD0D0D0), width: 1),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(2),
                              ),
                            ),
                            child: Text(
                              'Close',
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.titleSmall(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.black87,
                              ),
                            ),
                          ),

                          SizedBox(width: 20),
                          // Previous button (show in entity, workflow, and workflowLO creation steps)
                          if (provider.currentStep ==
                                  WorkflowStep.entityCreation ||
                              provider.currentStep ==
                                  WorkflowStep.workflowCreation ||
                              provider.currentStep ==
                                  WorkflowStep.workflowLoCreation)
                            ElevatedButton(
                              onPressed: provider.goToPreviousStep,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.grey.shade600,
                                padding: EdgeInsets.symmetric(
                                    horizontal: 20, vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(2),
                                ),
                              ),
                              child: Text(
                                'Previous',
                                style: FontManager.getCustomStyle(
                                  fontSize:
                                      ResponsiveFontSizes.titleSmall(context),
                                  fontWeight: FontWeight.w500,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: Colors.white,
                                ),
                              ),
                            ),

                          if (provider.currentStep ==
                                  WorkflowStep.entityCreation ||
                              provider.currentStep ==
                                  WorkflowStep.workflowCreation ||
                              provider.currentStep ==
                                  WorkflowStep.workflowLoCreation)
                            SizedBox(width: 20),

                          // Main action button
                          ElevatedButton(
                            onPressed: _isValidating(provider)
                                ? null
                                : () => _handleButtonPress(provider),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: _isValidating(provider)
                                  ? Colors.grey.shade400
                                  : Color(0xff0058FF),
                              padding: EdgeInsets.symmetric(
                                  horizontal: 20, vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ),
                            child: _isValidating(provider)
                                ? Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      SizedBox(
                                        width: 16,
                                        height: 16,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                                  Colors.white),
                                        ),
                                      ),
                                      SizedBox(width: 8),
                                      Text(
                                        'Validating...',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 14,
                                          fontFamily: 'TiemposText',
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                    ],
                                  )
                                : Text(
                                    _getButtonText(provider),
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 14,
                                      fontFamily: 'TiemposText',
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                          ),
                        ],
                      ),
                      SizedBox(height: 8),
                    ],
                  ),
                ),

                (provider.showSidePanel ||
                        provider.showWorkflowLoSidePanel ||
                        provider.showSideEntityPanel)
                    ? Container(
                        width: MediaQuery.of(context).size.width >= 1900
                            ? AppSpacing.spaceBetweenMainStatic
                            : AppSpacing.spaceBetweenMainStatic)
                    : Expanded(child: SizedBox())
              ],
            ),
          ),
          // Side panel for role details or workflowLO details
          if (provider.showSidePanel)
            ResizablePanel(
              width: provider.sidePanelWidth,
              minWidth: provider.minSidePanelWidth,
              maxWidth: provider.maxSidePanelWidth,
              handlePosition: ResizeHandlePosition.left,
              onResize: (newWidth) {
                provider.updateSidePanelWidth(newWidth);
              },
              child: RoleDetailsPanel(
                role: provider.selectedRole!,
                onClose: () {
                  provider.hideSidePanel();
                },
                showLegacySections:
                    false, // Use new dedicated fields (CR/KPI/DA)
                users: provider
                    .extractedUsers, // Pass user data for role assignments
              ),
            )
          else if (provider.showWorkflowLoSidePanel)
            ResizablePanel(
              width: provider.sidePanelWidth,
              minWidth: provider.minSidePanelWidth,
              maxWidth: provider.maxSidePanelWidth,
              handlePosition: ResizeHandlePosition.left,
              onResize: (newWidth) {
                provider.updateSidePanelWidth(newWidth);
              },
              child: WorkflowLoDetailsPanel(
                workflowLoData: provider.selectedWorkflowLoData!,
                onClose: () {
                  provider.hideWorkflowLoSidePanel();
                },
              ),
            )
          else if (provider.showSideEntityPanel)
            ResizablePanel(
              width: provider.sidePanelWidth,
              minWidth: provider.minSidePanelWidth,
              maxWidth: provider.maxSidePanelWidth,
              handlePosition: ResizeHandlePosition.left,
              onResize: (newWidth) {
                provider.updateSidePanelWidth(newWidth);
              },
              child: EntityDetailsPanel(
                entity: provider.selectedEntity!,
                onClose: () => provider.hideEntitySidePanel(),
                chatController: null,
                onSendMessage: null,
                globalEntityElements: globalEntityElements,
              ),
            )
        ],
      ),
    );
  }

  Widget rightSideTooltips(ManualCreationProvider provider) {
    return Padding(
      padding: const EdgeInsets.only(right: 5.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          // SizedBox(height: 20), // Align with text field

          // Agents icon
          _buildIconWithTooltip(
            provider: provider,
            iconPath: 'assets/images/agent.svg',
            tooltipText: WorkflowStep.agentCreation.toString(),
            toolTip: "Agents",
            onTap: provider.handleAgentsTap,
          ),

          SizedBox(height: 10),

          // Entities icon
          _buildIconWithTooltip(
            provider: provider,
            iconPath: 'assets/images/cube-box.svg',
            tooltipText: WorkflowStep.entityCreation.toString(),
            toolTip: "Data Sets",
            onTap: provider.handleDataSetsTap,
          ),

          SizedBox(height: 10),

          // Workflows icon
          _buildIconWithTooltip(
            provider: provider,
            iconPath: 'assets/images/square-box-uncheck.svg',
            tooltipText: WorkflowStep.workflowCreation.toString(),
            toolTip: "Work Flows",
            onTap: provider.handleWorkflowsTap,
          ),
          SizedBox(height: 10),

          // WorkflowLOs icon
          _buildIconWithTooltip(
            provider: provider,
            iconPath: 'assets/images/workflow_lo.svg',
            tooltipText: WorkflowStep.workflowLoCreation.toString(),
            toolTip: "Work Flow LO",
            onTap: provider.extractedWorkflowData == null ||
                    provider.extractedWorkflowData?.first['workFlowDetails']
                        ?.localObjectivesList.isEmpty
                ? () {
                    _showErrorAlertDialog(context, 'Workflow Validation Error',
                        "Cannot proceed to LO without GO");
                    return;
                  }
                : provider.handleWorkflowsloTap,
          ),
        ],
      ),
    );
  }

  // Show validation results in alert dialog
  // Show validation results in alert dialog
  void _showValidationAlertDialog(
      BuildContext context, ManualCreationProvider provider) {
    // Only show alerts for the current workflow step
    switch (provider.currentStep) {
      case WorkflowStep.agentCreation:
        _handleAgentValidationAlerts(context, provider);
        break;
      case WorkflowStep.entityCreation:
        _handleEntityValidationAlerts(context, provider);
        break;
      case WorkflowStep.workflowCreation:
        _handleWorkflowValidationAlerts(context, provider);
        break;
      case WorkflowStep.workflowLoCreation:
        _handleWorkflowLoValidationAlerts(context, provider);
        break;
    }
  }

// Handle agent validation alerts
  void _handleAgentValidationAlerts(
      BuildContext context, ManualCreationProvider provider) {
    if (provider.validationError != null) {
      _lastShownError = provider.validationError;
      _showErrorAlertDialog(
          context, 'Agent Validation Error', provider.validationError!);
    } else if (provider.validationResult != null &&
        provider.validationResult!.validationErrors != null &&
        provider.validationResult!.validationErrors!.isNotEmpty) {
      final errors = provider.validationResult!.validationErrors!;
      final errorMessages =
          errors.map((e) => '• ${e.message ?? 'Validation error'}').toList();

      if (_lastShownResult != errorMessages.join('\n')) {
        _lastShownResult = errorMessages.join('\n');
        _showValidationErrorsAlertDialog(
            context, 'Agent Validation Errors', errorMessages);
      }
    } else if (provider.validationResult != null &&
        provider.showAgentTable &&
        _lastShownResult != 'agent_success') {
      _lastShownResult = 'agent_success';
      _showSuccessAlertDialog(context, 'Agent Validation Successful',
          'Agent data loaded successfully.');
    }
  }

// Handle entity validation alerts
  void _handleEntityValidationAlerts(
      BuildContext context, ManualCreationProvider provider) {
    if (provider.entityValidationError != null) {
      _lastShownError = provider.entityValidationError;
      _showErrorAlertDialog(
          context, 'Entity Validation Error', provider.entityValidationError!);
    } else if (provider.entityValidationResult != null &&
        provider.showEntityTable &&
        _lastShownResult != 'entity_success') {
      _lastShownResult = 'entity_success';
      _showSuccessAlertDialog(context, 'Entity Validation Successful',
          'Entity data loaded successfully.');
    }
  }

// Handle workflow validation alerts
  void _handleWorkflowValidationAlerts(
      BuildContext context, ManualCreationProvider provider) {
    if (provider.workflowValidationError != null) {
      _lastShownError = provider.workflowValidationError;
      _showErrorAlertDialog(context, 'Workflow Validation Error',
          provider.workflowValidationError!);
    } else if (provider.workflowValidationResult != null &&
        provider.showWorkflowTable &&
        _lastShownResult != 'workflow_success') {
      _lastShownResult = 'workflow_success';
      _showSuccessAlertDialog(context, 'Workflow Validation Successful',
          'Workflow data loaded successfully.');
    }
  }

// Handle workflow LO validation alerts
  void _handleWorkflowLoValidationAlerts(
      BuildContext context, ManualCreationProvider provider) {
    if (provider.workflowLoValidationError != null) {
      _lastShownError = provider.workflowLoValidationError;
      _showErrorAlertDialog(context, 'Workflow LO Validation Error',
          provider.workflowLoValidationError!);
    } else if (provider.workflowLoValidationResult != null &&
        provider.showWorkflowLoTable &&
        _lastShownResult != 'workflow_lo_success') {
      _lastShownResult = 'workflow_lo_success';
      _showSuccessAlertDialog(context, 'Workflow LO Validation Successful',
          'Workflow LO data loaded successfully.');
    }
  }

  // void _showValidationAlertDialog(
  //     BuildContext context, ManualCreationProvider provider) {
  //   // Handle agent validation errors
  //   if (provider.validationError != null &&
  //       _lastShownError != provider.validationError) {
  //     _lastShownError = provider.validationError;
  //     _showErrorAlertDialog(
  //         context, 'Agent Validation Error', provider.validationError!);
  //   } else if (provider.validationResult != null &&
  //       provider.validationResult!.validationErrors != null &&
  //       provider.validationResult!.validationErrors!.isNotEmpty) {
  //     // Show agent validation errors in alert dialog
  //     final errors = provider.validationResult!.validationErrors!;
  //     final errorMessages =
  //         errors.map((e) => '• ${e.message ?? 'Validation error'}').toList();

  //     if (_lastShownResult != errorMessages.join('\n')) {
  //       _lastShownResult = errorMessages.join('\n');
  //       _showValidationErrorsAlertDialog(
  //           context, 'Agent Validation Errors', errorMessages);
  //     }
  //   } else if (provider.validationResult != null &&
  //       provider.showAgentTable &&
  //       _lastShownResult != 'agent_success') {
  //     // Show success message when agent table is shown
  //     _lastShownResult = 'agent_success';
  //     _showSuccessAlertDialog(context, 'Agent Validation Successful',
  //         'Agent data loaded successfully.');
  //   }

  //   // Handle entity validation errors
  //   else if (provider.entityValidationError != null &&
  //       _lastShownError != provider.entityValidationError) {
  //     _lastShownError = provider.entityValidationError;
  //     _showErrorAlertDialog(
  //         context, 'Entity Validation Error', provider.entityValidationError!);
  //   } else if (provider.entityValidationResult != null &&
  //       provider.showEntityTable &&
  //       _lastShownResult != 'entity_success') {
  //     // Show success message when entity table is shown
  //     _lastShownResult = 'entity_success';
  //     _showSuccessAlertDialog(context, 'Entity Validation Successful',
  //         'Entity data loaded successfully.');
  //   }

  //   // Handle workflow validation errors
  //   else if (provider.workflowValidationError != null &&
  //       _lastShownError != provider.workflowValidationError) {
  //     _lastShownError = provider.workflowValidationError;
  //     _showErrorAlertDialog(context, 'Workflow Validation Error',
  //         provider.workflowValidationError!);
  //   } else if (provider.workflowValidationResult != null &&
  //       provider.showWorkflowTable &&
  //       _lastShownResult != 'workflow_success') {
  //     // Show success message when workflow table is shown
  //     _lastShownResult = 'workflow_success';
  //     _showSuccessAlertDialog(context, 'Workflow Validation Successful',
  //         'Workflow data loaded successfully.');
  //   }

  //   // Handle workflowLO validation errors
  //   else if (provider.workflowLoValidationError != null &&
  //       _lastShownError != provider.workflowLoValidationError) {
  //     _lastShownError = provider.workflowLoValidationError;
  //     _showErrorAlertDialog(context, 'Workflow LO Validation Error',
  //         provider.workflowLoValidationError!);
  //   } else if (provider.workflowLoValidationResult != null &&
  //       provider.showWorkflowLoTable &&
  //       _lastShownResult != 'workflow_lo_success') {
  //     // Show success message when workflow LO table is shown
  //     _lastShownResult = 'workflow_lo_success';
  //     _showSuccessAlertDialog(context, 'Workflow LO Validation Successful',
  //         'Workflow LO data loaded successfully.');
  //   }
  // }

  // Show error alert dialog
  void _showErrorAlertDialog(
      BuildContext context, String title, String message) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Center(
          child: Material(
            type: MaterialType.transparency,
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.4,
                maxHeight: MediaQuery.of(context).size.height * 0.8,
              ),
              child: AlertDialog(
                title: Row(
                  children: [
                    Icon(Icons.error_outline, color: Colors.red, size: 24),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        title,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'TiemposText',
                          color: Colors.red,
                        ),
                      ),
                    ),
                  ],
                ),
                content: Text(
                  '• $message',
                  style: TextStyle(
                    fontSize: 14,
                    fontFamily: 'TiemposText',
                    color: Colors.black87,
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: Text(
                      'OK',
                      style: TextStyle(
                        fontSize: 14,
                        fontFamily: 'TiemposText',
                        color: Color(0xff0058FF),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Show validation errors alert dialog
  void _showValidationErrorsAlertDialog(
      BuildContext context, String title, List<String> messages) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Center(
          child: Material(
            type: MaterialType.transparency,
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.4,
                maxHeight: MediaQuery.of(context).size.height * 0.8,
              ),
              child: AlertDialog(
                title: Row(
                  children: [
                    Icon(Icons.warning_outlined,
                        color: Colors.orange, size: 24),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        title,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'TiemposText',
                          color: Colors.orange,
                        ),
                      ),
                    ),
                  ],
                ),
                content: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: messages
                        .map((message) => Padding(
                              padding: EdgeInsets.only(bottom: 8),
                              child: Text(
                                message,
                                style: TextStyle(
                                  fontSize: 14,
                                  fontFamily: 'TiemposText',
                                  color: Colors.black87,
                                ),
                              ),
                            ))
                        .toList(),
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: Text(
                      'OK',
                      style: TextStyle(
                        fontSize: 14,
                        fontFamily: 'TiemposText',
                        color: Color(0xff0058FF),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Show success alert dialog
  void _showSuccessAlertDialog(
      BuildContext context, String title, String message) {
    // showDialog(
    //   context: context,
    //   barrierDismissible: true,
    //   builder: (BuildContext context) {
    //     return Center(
    //       child: Material(
    //         type: MaterialType.transparency,
    //         child: Container(
    //           constraints: BoxConstraints(
    //             maxWidth: MediaQuery.of(context).size.width * 0.4,
    //             maxHeight: MediaQuery.of(context).size.height * 0.8,
    //           ),
    //           child: AlertDialog(
    //             title: Row(
    //               children: [
    //                 Icon(Icons.check_circle_outline,
    //                     color: Colors.green, size: 24),
    //                 SizedBox(width: 8),
    //                 Expanded(
    //                   child: Text(
    //                     title,
    //                     style: TextStyle(
    //                       fontSize: 18,
    //                       fontWeight: FontWeight.w600,
    //                       fontFamily: 'TiemposText',
    //                       color: Colors.green,
    //                     ),
    //                   ),
    //                 ),
    //               ],
    //             ),
    //             content: Text(
    //               '• $message',
    //               style: TextStyle(
    //                 fontSize: 14,
    //                 fontFamily: 'TiemposText',
    //                 color: Colors.black87,
    //               ),
    //             ),
    //             actions: [
    //               TextButton(
    //                 onPressed: () {
    //                   Navigator.of(context).pop();
    //                 },
    //                 child: Text(
    //                   'OK',
    //                   style: TextStyle(
    //                     fontSize: 14,
    //                     fontFamily: 'TiemposText',
    //                     color: Color(0xff0058FF),
    //                   ),
    //                 ),
    //               ),
    //             ],
    //           ),
    //         ),
    //       ),
    //     );
    //   },
    // );
  }

  // Helper methods for workflow
  String _getTitle(ManualCreationProvider provider) {
    switch (provider.currentStep) {
      case WorkflowStep.agentCreation:
        return provider.showAgentTable ? 'Agents' : 'Create Your Agents';
      case WorkflowStep.entityCreation:
        return provider.showEntityTable ? 'Entity List' : 'Create Your Entity';
      case WorkflowStep.workflowCreation:
        return provider.showWorkflowTable
            ? 'Workflow List'
            : 'Create Your Workflow';
      case WorkflowStep.workflowLoCreation:
        return provider.showWorkflowLoTable
            ? 'Workflow LO List'
            : 'Add your LO Details';
    }
  }

  String _getButtonText(ManualCreationProvider provider) {
    switch (provider.currentStep) {
      case WorkflowStep.agentCreation:
        return provider.showAgentTable ? 'Next' : 'Validate';
      case WorkflowStep.entityCreation:
        return provider.showEntityTable ? 'Next' : 'Validate';
      case WorkflowStep.workflowCreation:
        return provider.showWorkflowTable ? 'Next' : 'Validate';
      case WorkflowStep.workflowLoCreation:
        return provider.showWorkflowLoTable ? 'Finish' : 'Validate';
    }
  }

  bool _isValidating(ManualCreationProvider provider) {
    return provider.isValidating ||
        provider.isValidatingEntity ||
        provider.isValidatingWorkflow ||
        provider.isValidatingWorkflowLo;
  }

  void _handleButtonPress(ManualCreationProvider provider) {
    switch (provider.currentStep) {
      case WorkflowStep.agentCreation:
        if (provider.showAgentTable) {
          provider.goToNextStep();
        } else {
          provider.handleValidate();
        }
        break;
      case WorkflowStep.entityCreation:
        if (provider.showEntityTable) {
          provider.goToNextStep();
        } else {
          provider.handleValidate();
        }
        break;
      case WorkflowStep.workflowCreation:
        if (provider.showWorkflowTable) {
          provider.goToNextStep();
        } else {
          provider.handleValidate();
        }
        break;
      case WorkflowStep.workflowLoCreation:
        if (provider.showWorkflowLoTable) {
          // Handle finish action (navigate away or complete workflow)
          _handleFinishWorkflow(provider);
        } else {
          provider.handleValidate();
        }
        break;
    }
  }

  void _handleFinishWorkflow(ManualCreationProvider provider) {
    // Show success message and navigate back or reset
    _showSuccessAlertDialog(context, 'Workflow Complete',
        'Agent, entity, workflow, and workflow LO creation completed successfully.');
    // Optionally reset the workflow or navigate away
    // provider.resetWorkflow();
  }

  Widget _buildWorkflowContent(
      BuildContext context, ManualCreationProvider provider) {
    // Otherwise, use the existing workflow logic
    switch (provider.currentStep) {
      case WorkflowStep.agentCreation:
        return provider.showAgentTable
            ? (provider.showAgentTreeView
                ? AgentHierarchyChart(
                    provider: provider,
                    onNodeTap: (node) {
                      // Handle node tap - could show details or select role
                      print('Agent node tapped: ${node.name}');
                    },
                  )
                : AgentTable(
                    provider: provider,
                    onRoleSelected: (role) {
                      provider.showRoleDetailsPanel(role);
                    },
                  ))
            : _buildTextField(context, provider);
      case WorkflowStep.entityCreation:
        return provider.showEntityTable
            ? EntityTable(
                provider: provider,
                onEntitySelected: (entity) {
                  provider.showEntityDetailsPanel(entity);
                },
                selectedSectionIndex: selectedSectionIndex,
                onSectionIndexChanged: (index) {
                  setState(() {
                    selectedSectionIndex = index;
                  });
                },
              )
            : _buildTextField(context, provider);
      case WorkflowStep.workflowCreation:
        return provider.showWorkflowTable
            ? WorkflowTable(
                provider: provider,
                onNodeSelected: () {
                  if (provider.workflowLoValidationResult != null) {
                    provider.showWorkflowLoDetailsPanel(
                        provider.workflowLoValidationResult!);
                  }
                },
              )
            : _buildTextField(context, provider);
      case WorkflowStep.workflowLoCreation:
        return provider.showWorkflowLoTable
            ? WorkflowLoWithWorkflowStructure(
                provider: provider,
                onNodeSelected: () {
                  // if (provider.workflowLoValidationResult != null) {
                  //   provider.showWorkflowLoDetailsPanel(
                  //       provider.workflowLoValidationResult!);
                  // }
                },
              )
            : LoEntryScreen(
                textFieldWidget: _buildTextField(context, provider));
    }
  }

  Widget _buildTextField(
      BuildContext context, ManualCreationProvider provider) {
    return Container(
      decoration: BoxDecoration(
        // border: Border.all(color: Color(0xffD0D0D0), width: 1),
        border: const Border(
          left: BorderSide(color: Color(0xffD0D0D0), width: 1),
          right: BorderSide(color: Color(0xffD0D0D0), width: 1),
          bottom: BorderSide(color: Color(0xffD0D0D0), width: 1),
        ),
        borderRadius: BorderRadius.circular(0),
      ),
      child: Theme(
        data: Theme.of(context).copyWith(
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          hoverColor: Colors.transparent,
          focusColor: Colors.transparent,
        ),
        child: TextField(
          controller: provider.textController,
          maxLines: null,
          expands: true,
          textAlignVertical: TextAlignVertical.top,
          decoration: InputDecoration(
            hintStyle: TextStyle(
              color: Colors.grey.shade500,
              fontSize: 16,
              fontFamily: 'TiemposText',
            ),
            border: InputBorder.none,
            enabledBorder: InputBorder.none,
            focusedBorder: InputBorder.none,
            contentPadding: EdgeInsets.all(16),
          ),
          style: TextStyle(
            fontSize: 16,
            fontFamily: 'TiemposText',
            color: Colors.black,
          ),
        ),
      ),
    );
  }

  Widget _buildIconWithTooltip({
    required ManualCreationProvider provider,
    required String iconPath,
    required String tooltipText,
    required VoidCallback onTap,
    required String toolTip,
  }) {
    return _IconWithTooltip(
      iconPath: iconPath,
      tooltipText: toolTip,
      isSelected: provider.currentStep.toString() ==
          tooltipText, // Check if this icon is selected
      onTap: () {
        // Set the active icon view
        // setState(() {
        //   _activeIconView = tooltipText;
        // });
        if (!(provider.extractedWorkflowData == null ||
            provider.extractedWorkflowData?.first['workFlowDetails']
                ?.localObjectivesList.isEmpty)) {
          provider.goToNextStep();
        }
        // Call the original onTap if needed
        onTap();
      },
    );
  }

  bool _shouldShowAddIcon(ManualCreationProvider provider) {
    switch (provider.currentStep) {
      case WorkflowStep.agentCreation:
        return provider.showAgentTable;
      case WorkflowStep.entityCreation:
        return provider.showEntityTable;
      case WorkflowStep.workflowCreation:
        return provider.showWorkflowTable;
      case WorkflowStep.workflowLoCreation:
        return provider.showWorkflowLoTable;
    }
  }

  generateAttributeSentence(AttributeMetadata attribute) {
    return Text.rich(TextSpan(
        text: "${attribute.displayName} ",
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold,
          color: Colors.black,
          fontFamily: 'TiemposText',
        ),
        children: <InlineSpan>[
          TextSpan(
            text:
                "is ${attribute.keyType} key and ${attribute.description}. Data type is ${attribute.dataType}${attribute.values != "N/A" ? " ( ${attribute.values} )" : ""}. Error message: \"${attribute.errorMessage}\". ${attribute.required} field.",
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.normal,
              color: Colors.black,
              fontFamily: 'TiemposText',
            ),
          )
        ]));
  }
}

class _IconWithTooltip extends StatefulWidget {
  final String iconPath;
  final String tooltipText;
  final VoidCallback onTap;
  final bool isSelected;

  const _IconWithTooltip({
    required this.iconPath,
    required this.tooltipText,
    required this.onTap,
    this.isSelected = false,
  });

  @override
  State<_IconWithTooltip> createState() => _IconWithTooltipState();
}

class _IconWithTooltipState extends State<_IconWithTooltip> {
  bool isHovered = false;
  final GlobalKey _iconKey = GlobalKey();
  OverlayEntry? _overlayEntry;

  @override
  void dispose() {
    _removeTooltip();
    super.dispose();
  }

  void _showTooltip() {
    try {
      _removeTooltip();

      // Get the position of the icon
      final RenderBox? renderBox =
          _iconKey.currentContext?.findRenderObject() as RenderBox?;
      if (renderBox == null) return;

      final position = renderBox.localToGlobal(Offset.zero);
      final size = renderBox.size;

      _overlayEntry = OverlayEntry(
        builder: (context) => Positioned(
          left: position.dx + size.width + 8, // 8px gap from icon
          top: position.dy + (size.height / 2) - 14, // Center vertically
          child: Material(
            color: Colors.transparent,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black,
                // borderRadius: BorderRadius.circular(2),
              ),
              padding: EdgeInsets.symmetric(
                horizontal: AppSpacing.xxs,
                vertical: AppSpacing.xxs,
              ),
              child: Text(
                widget.tooltipText,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.w400,
                  fontFamily: 'TiemposText',
                ),
              ),
            ),
          ),
        ),
      );

      // Check if the context is still valid before inserting the overlay
      if (!mounted) return;

      Overlay.of(context).insert(_overlayEntry!);
    } catch (e) {
      // Safely handle any errors that might occur when showing the tooltip
      Logger.error('Error showing tooltip: $e');
      _overlayEntry = null;
    }
  }

  void _removeTooltip() {
    try {
      if (_overlayEntry != null) {
        _overlayEntry!.remove();
        _overlayEntry = null;
      }
    } catch (e) {
      // Safely handle any errors that might occur when removing the overlay
      Logger.error('Error removing tooltip: $e');
      _overlayEntry = null;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Determine the icon color based on selected and hover states
    Color iconColor;
    if (widget.isSelected) {
      iconColor = Color(0xff0058FF); // Blue when selected
    } else if (isHovered) {
      iconColor = Color(0xff0058FF); // Blue when hovered
    } else {
      iconColor = Color(0xff797676); // Default gray
    }

    return MouseRegion(
      cursor: SystemMouseCursors.click,
      onEnter: (_) {
        setState(() => isHovered = true);
        _showTooltip();
      },
      onExit: (_) {
        setState(() => isHovered = false);
        _removeTooltip();
      },
      child: GestureDetector(
        onTap: widget.onTap,
        child: Container(
          key: _iconKey,
          padding: EdgeInsets.all(4), // Add padding for background
          decoration: BoxDecoration(
            color: widget.isSelected
                ? Color(0xff0058FF).withValues(
                    alpha: 0.1) // Light blue background when selected
                : Colors.transparent,
            borderRadius: BorderRadius.circular(4),
          ),
          child: SvgPicture.asset(
            widget.iconPath,
            width: 11,
            height: 11,

            // Add stroke width for bold effect when selected
            colorFilter: ColorFilter.mode(iconColor, BlendMode.srcIn),
          ),
        ),
      ),
    );
  }
}
